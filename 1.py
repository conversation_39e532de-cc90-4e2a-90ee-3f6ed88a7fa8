import pandas as pd
import math
import random
import numpy as np
from time import time
import matplotlib.pyplot as plt
import matplotlib as mpl
from pathlib import Path
import os

# 启用pandas性能优化
pd.set_option('compute.use_bottleneck', True)
pd.set_option('compute.use_numexpr', True)

# 设置Mac自带字体
plt.rcParams['font.family'] = 'Arial Unicode MS'  # Mac自带的中文字体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号

def load_real_data():
    """
    加载并处理附件2和附件3的真实数据。
    生成模拟的坐标数据。
    """
    start_time = time()
    print("🔄 正在加载数据...")

    # --- 加载附件2: 4类运输车辆参数 ---
    try:
        print("正在读取附件2...")
        df_vehicles = pd.read_excel('附件2.xlsx')
        print(f"附件2数据形状: {df_vehicles.shape}")
        print(f"附件2列名: {df_vehicles.columns.tolist()}")
        print("附件2前几行数据:")
        print(df_vehicles.head())
        print("-" * 30)

        if df_vehicles.empty:
            raise ValueError("附件2文件为空")

        vehicle_params = {}
        for index, row in df_vehicles.iterrows():
            print(f"处理第{index}行数据: {row.tolist()}")

            # 获取列名
            cols = df_vehicles.columns.tolist()
            print(f"可用列: {cols}")

            if len(cols) < 4:
                raise ValueError(f"附件2列数不足，期望至少4列，实际{len(cols)}列")

            # 使用列索引而不是列名
            type_col = cols[0]
            q_col = cols[1]
            v_col = cols[2]
            c_col = cols[3]

            # 提取类型编号
            type_str = str(row[type_col])
            print(f"类型字符串: {type_str}")

            if '类型' in type_str:
                vehicle_type_index = int(type_str.split(' ')[1]) - 1
            else:
                # 尝试直接提取数字
                digits = ''.join(filter(str.isdigit, type_str))
                if digits:
                    vehicle_type_index = int(digits) - 1
                else:
                    vehicle_type_index = index  # 使用行索引作为备选

            print(f"车辆类型索引: {vehicle_type_index}")

            vehicle_params[vehicle_type_index] = {
                'Q': float(row[q_col]),
                'V': float(row[v_col]),
                'C': float(row[c_col])
            }
            print(f"车辆参数: {vehicle_params[vehicle_type_index]}")

        print(f"✅ 附件2车辆参数加载成功 ({time()-start_time:.2f}秒)")
        print(f"最终车辆参数: {vehicle_params}")
        print("-" * 50)
    except Exception as e:
        print(f"❌ 加载附件2失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, None

    # --- 加载附件3: 30个收集点的4类垃圾量分布 ---
    try:
        # 尝试多种可能的文件名和格式
        possible_files = [
            '附件3.xlsx - 附件330个收集点的4类垃圾量分布.csv',
            '附件3.csv',
            '附件3.xlsx'
        ]

        df_waste = None
        for file in possible_files:
            try:
                if file.endswith('.csv'):
                    df_waste = pd.read_csv(file)
                    break
                elif file.endswith('.xlsx'):
                    df_waste = pd.read_excel(file)
                    break
            except:
                continue

        if df_waste is None:
            raise FileNotFoundError("无法找到附件3文件，请确保文件存在且格式正确")

        num_customers = len(df_waste)

        # 将数据解析为程序所需的字典格式 - 使用更高效的向量化操作
        demands_w = {k: {} for k in range(4)}  # 重量
        demands_v = {k: {} for k in range(4)}  # 体积

        # 尝试多种可能的列名
        if '收集点编号' in df_waste.columns:
            id_col = '收集点编号'
        elif '编号' in df_waste.columns:
            id_col = '编号'
        else:
            # 假设第一列是编号
            id_col = df_waste.columns[0]

        # 尝试识别垃圾类型列名
        waste_cols = {}
        for k in range(4):
            waste_types = [
                (f'厨余垃圾重量(w{k+1})', f'厨余垃圾体积(v{k+1})'),
                (f'可回收物重量(w{k+1})', f'可回收物体积(v{k+1})'),
                (f'有害垃圾重量(w{k+1})', f'有害垃圾体积(v{k+1})'),
                (f'其他垃圾重量(w{k+1})', f'其他垃圾体积(v{k+1})')
            ]

            # 默认列名
            default_cols = (f'w{k+1}', f'v{k+1}')

            # 尝试找到匹配的列名
            found = False
            for w_col, v_col in waste_types:
                if w_col in df_waste.columns and v_col in df_waste.columns:
                    waste_cols[k] = (w_col, v_col)
                    found = True
                    break

            # 如果没找到，尝试简化的列名
            if not found:
                for col in df_waste.columns:
                    if f'w{k+1}' in col.lower():
                        w_col = col
                        for vcol in df_waste.columns:
                            if f'v{k+1}' in vcol.lower():
                                v_col = vcol
                                waste_cols[k] = (w_col, v_col)
                                found = True
                                break
                        if found:
                            break

            # 如果还是没找到，使用默认的列索引
            if not found:
                waste_cols[k] = (df_waste.columns[2*k+1], df_waste.columns[2*k+2])

        for index, row in df_waste.iterrows():
            customer_id = int(row[id_col])
            for k in range(4):
                w_col, v_col = waste_cols[k]
                if pd.notna(row[w_col]) and float(row[w_col]) > 0:
                    demands_w[k][customer_id] = float(row[w_col])
                    demands_v[k][customer_id] = float(row[v_col])

        print(f"✅ 附件3中 {num_customers} 个收集点的垃圾数据加载成功 ({time()-start_time:.2f}秒)")
        print("-" * 50)
    except Exception as e:
        print(f"❌ 加载附件3失败: {e}")
        return None, None, None, None

    # --- 生成模拟坐标 ---
    # 使用随机种子确保结果可重现
    random.seed(42)
    nodes = {0: {'x': 0, 'y': 0}}  # 0号点是处理厂
    for i in range(1, num_customers + 1):
        nodes[i] = {'x': random.randint(-80, 80), 'y': random.randint(-80, 80)}
    print(f"ℹ️ 已为30个收集点生成模拟地理坐标 ({time()-start_time:.2f}秒)\n")

    return nodes, demands_w, demands_v, vehicle_params
def clarke_wright_solver(nodes, demands_w_k, demands_v_k, Q_k, V_k):
    """
    使用 Clarke & Wright 节约算法求解单个类型的CVRP问题。
    优化版本：预计算距离矩阵，使用NumPy加速计算
    """
    depot = 0
    customer_nodes = list(demands_w_k.keys())

    if not customer_nodes:
        return [], 0.0

    # 预计算距离矩阵
    n = max(customer_nodes) + 1 if customer_nodes else 1
    dist_matrix = np.zeros((n, n))
    for i in range(n):
        if i not in nodes:
            continue
        for j in range(i+1, n):
            if j not in nodes:
                continue
            dist = math.sqrt((nodes[i]['x'] - nodes[j]['x'])**2 + (nodes[i]['y'] - nodes[j]['y'])**2)
            dist_matrix[i, j] = dist
            dist_matrix[j, i] = dist

    # 计算节约值
    savings = []
    for i in customer_nodes:
        for j in customer_nodes:
            if i < j:
                s_ij = dist_matrix[depot, i] + dist_matrix[depot, j] - dist_matrix[i, j]
                savings.append({'i': i, 'j': j, 'value': s_ij})

    # 按节约值降序排序
    savings.sort(key=lambda x: x['value'], reverse=True)

    # 初始化路径
    routes = {node: [depot, node, depot] for node in customer_nodes}

    # 合并路径
    for s in savings:
        i, j = s['i'], s['j']
        route_i = routes.get(i)
        route_j = routes.get(j)

        # 检查是否可以合并
        if route_i and route_j and route_i != route_j and route_i[-2] == i and route_j[1] == j:
            merged_nodes = route_i[1:-1] + route_j[1:-1]

            # 检查重量约束
            total_weight = sum(demands_w_k.get(n, 0) for n in merged_nodes)
            if total_weight > Q_k:
                continue

            # 检查体积约束
            total_volume = sum(demands_v_k.get(n, 0) for n in merged_nodes)
            if total_volume > V_k:
                continue

            # 合并路径
            new_route = route_i[:-1] + route_j[1:]
            for node in new_route[1:-1]:
                routes[node] = new_route

    # 获取最终路径并计算总距离
    final_routes = list(set(map(tuple, routes.values())))
    total_dist = 0
    for r in final_routes:
        route_dist = 0
        for idx in range(len(r) - 1):
            route_dist += dist_matrix[r[idx], r[idx+1]]
        total_dist += route_dist

    return final_routes, total_dist
def solve_and_report():
    """主执行函数，加载真实数据并生成最终报告"""
    total_start_time = time()
    print("🚚 开始执行基于附件数据的城市四分类垃圾运输路径优化...\n")

    # 加载数据
    nodes, demands_w, demands_v, vehicle_params = load_real_data()
    if None in (nodes, demands_w, demands_v, vehicle_params):
        print("❌ 数据加载失败，无法继续执行")
        return

    waste_names = {
        0: "厨余垃圾",
        1: "可回收物",
        2: "有害垃圾",
        3: "其他垃圾"
    }
    total_overall_cost = 0
    total_vehicles = 0
    total_distance = 0

    # 结果汇总表
    results_summary = []
    all_routes = []

    # 循环处理四种垃圾类型
    for k in range(4):
        type_start_time = time()
        print(f"\n🔄 正在处理 {waste_names[k]}...")

        # 从加载的数据中获取该类型垃圾的任务和车辆参数
        demands_w_k = demands_w[k]
        demands_v_k = demands_v[k]
        params_k = vehicle_params[k]
        Q_k, V_k, C_k = params_k['Q'], params_k['V'], params_k['C']

        # 调用求解器处理当前子问题
        final_routes, total_dist = clarke_wright_solver(
            nodes, demands_w_k, demands_v_k, Q_k, V_k
        )

        # 保存路径结果
        all_routes.append(final_routes)

        # 计算并累加成本
        type_cost = total_dist * C_k
        total_overall_cost += type_cost
        total_vehicles += len(final_routes)
        total_distance += total_dist

        # 保存结果摘要
        results_summary.append({
            'type': k+1,
            'name': waste_names[k],
            'vehicles': len(final_routes),
            'distance': total_dist,
            'cost': type_cost,
            'routes': final_routes
        })

        # --- 打印该类型垃圾的详细报告 ---
        print(f"\n--- 类别 {k+1}: {waste_names[k]} 运输方案 ---")
        print(f"车辆参数: 最大载重={Q_k}吨, 最大容积={V_k}m³, 单位成本={C_k}元/km")

        if not final_routes:
            print("结果: 今日无此类型垃圾的运输任务。")
            print("-" * 50)
            continue

        print(f"结果: 共需 {len(final_routes)} 辆车, 总行驶距离: {total_dist:.2f} km, 运输成本: {type_cost:.2f} 元")
        for i, route in enumerate(final_routes):
            # 计算并显示每条路径的负载率
            route_w = sum(demands_w_k.get(n, 0) for n in route if n != 0)
            route_v = sum(demands_v_k.get(n, 0) for n in route if n != 0)
            w_load_ratio = (route_w / Q_k) * 100
            v_load_ratio = (route_v / V_k) * 100
            print(f"  - 车辆 {i+1} 路径: {' → '.join(map(str, route))}")
            print(f"    负载详情: 重量 {route_w:.2f}t ({w_load_ratio:.1f}%), "
                  f"体积 {route_v:.2f}m³ ({v_load_ratio:.1f}%)")

        print(f"✅ {waste_names[k]}处理完成 ({time()-type_start_time:.2f}秒)")
        print("-" * 50)

    # 打印汇总表
    print("\n📊 四类垃圾运输方案汇总")
    print("-" * 60)
    print(f"{'类型':^10}|{'车辆数':^8}|{'距离(km)':^12}|{'成本(元)':^12}|{'占比(%)':^10}")
    print("-" * 60)
    for r in results_summary:
        cost_percent = (r['cost'] / total_overall_cost) * 100 if total_overall_cost > 0 else 0
        print(f"{r['name']:^10}|{r['vehicles']:^8}|{r['distance']:^12.2f}|{r['cost']:^12.2f}|{cost_percent:^10.1f}")
    print("-" * 60)
    print(f"{'总计':^10}|{total_vehicles:^8}|{total_distance:^12.2f}|{total_overall_cost:^12.2f}|{100:^10.1f}")

    total_time = time() - total_start_time
    print(f"\n✅ 全部分析完成。每日预估总运输成本为: {total_overall_cost:.2f} 元")
    print(f"⏱️ 总计算时间: {total_time:.2f}秒")

    # 可视化结果
    try:
        print("\n🎨 正在生成可视化结果...")
        visualize_routes(nodes, all_routes, waste_names, results_summary)
        print("✅ 可视化结果已保存到 results 目录")
    except Exception as e:
        print(f"❌ 生成可视化结果失败: {e}")

def visualize_routes(nodes, all_routes, waste_names, results_summary, save_dir='results'):
    """
    可视化所有路径和结果
    """
    # 创建保存目录
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # 设置颜色映射
    colors = ['#FF5733', '#33FF57', '#3357FF', '#F033FF']
    markers = ['o', 's', '^', 'D']

    # 1. 绘制总体路径图
    plt.figure(figsize=(12, 10))

    # 绘制处理厂（仓库）
    plt.scatter(nodes[0]['x'], nodes[0]['y'], c='black', s=200, marker='*',
                edgecolors='white', linewidths=2, zorder=10, label='处理厂')

    # 为每种垃圾类型绘制路径
    for k, routes in enumerate(all_routes):
        if not routes:
            continue

        # 绘制该类型的所有客户点
        customers = set()
        for route in routes:
            for node in route:
                if node != 0:  # 不是处理厂
                    customers.add(node)

        for customer in customers:
            plt.scatter(nodes[customer]['x'], nodes[customer]['y'],
                        c=colors[k], s=100, marker=markers[k], alpha=0.7,
                        edgecolors='white', linewidths=1, zorder=5)

        # 绘制路径
        for i, route in enumerate(routes):
            for j in range(len(route) - 1):
                plt.plot([nodes[route[j]]['x'], nodes[route[j+1]]['x']],
                         [nodes[route[j]]['y'], nodes[route[j+1]]['y']],
                         c=colors[k], linestyle='-', linewidth=1.5, alpha=0.6)

    # 添加图例和标题
    legend_elements = [plt.Line2D([0], [0], marker='*', color='w', markerfacecolor='black',
                                 markersize=15, label='处理厂')]

    for k in range(4):
        if all_routes[k]:
            legend_elements.append(plt.Line2D([0], [0], marker=markers[k], color='w',
                                             markerfacecolor=colors[k], markersize=10,
                                             label=f'{waste_names[k]}收集点'))

    plt.legend(handles=legend_elements, loc='upper right', fontsize=12)
    plt.title('四类垃圾收集路径规划总览', fontsize=16)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.axis('equal')

    # 保存图片
    plt.tight_layout()
    plt.savefig(f'{save_dir}/总体路径规划.png', dpi=300)

    # 2. 为每种垃圾类型单独绘制路径图
    for k in range(4):
        if not all_routes[k]:
            continue

        plt.figure(figsize=(10, 8))

        # 绘制处理厂
        plt.scatter(nodes[0]['x'], nodes[0]['y'], c='black', s=200, marker='*',
                    edgecolors='white', linewidths=2, zorder=10, label='处理厂')

        # 绘制该类型的所有客户点
        customers = set()
        for route in all_routes[k]:
            for node in route:
                if node != 0:  # 不是处理厂
                    customers.add(node)

        for customer in customers:
            plt.scatter(nodes[customer]['x'], nodes[customer]['y'],
                        c=colors[k], s=100, marker=markers[k], alpha=0.8,
                        edgecolors='white', linewidths=1, zorder=5)
            # 添加节点编号
            plt.annotate(str(customer), (nodes[customer]['x'], nodes[customer]['y']),
                         xytext=(5, 5), textcoords='offset points', fontsize=9)

        # 为每条路径使用不同深浅的颜色
        for i, route in enumerate(all_routes[k]):
            route_color = colors[k]
            # 绘制路径线
            for j in range(len(route) - 1):
                plt.plot([nodes[route[j]]['x'], nodes[route[j+1]]['x']],
                         [nodes[route[j]]['y'], nodes[route[j+1]]['y']],
                         c=route_color, linestyle='-', linewidth=2, alpha=0.7)
                # 添加箭头指示方向
                mid_x = (nodes[route[j]]['x'] + nodes[route[j+1]]['x']) / 2
                mid_y = (nodes[route[j]]['y'] + nodes[route[j+1]]['y']) / 2
                dx = nodes[route[j+1]]['x'] - nodes[route[j]]['x']
                dy = nodes[route[j+1]]['y'] - nodes[route[j]]['y']
                plt.arrow(mid_x - dx*0.1, mid_y - dy*0.1, dx*0.2, dy*0.2,
                          head_width=3, head_length=5, fc=route_color, ec=route_color)

            # 添加路径编号
            first_node = route[1]  # 第一个客户点
            plt.annotate(f'路径{i+1}', (nodes[first_node]['x'], nodes[first_node]['y']),
                         xytext=(-20, -20), textcoords='offset points',
                         bbox=dict(boxstyle="round,pad=0.3", fc="white", ec=route_color, alpha=0.8),
                         fontsize=10)

        # 添加标题和图例
        plt.title(f'{waste_names[k]}收集路径规划 - {len(all_routes[k])}辆车', fontsize=16)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.axis('equal')

        # 添加图例
        legend_elements = [
            plt.Line2D([0], [0], marker='*', color='w', markerfacecolor='black',
                      markersize=15, label='处理厂'),
            plt.Line2D([0], [0], marker=markers[k], color='w', markerfacecolor=colors[k],
                      markersize=10, label=f'{waste_names[k]}收集点')
        ]
        plt.legend(handles=legend_elements, loc='upper right', fontsize=12)

        # 保存图片
        plt.tight_layout()
        plt.savefig(f'{save_dir}/{waste_names[k]}路径规划.png', dpi=300)

    # 3. 绘制成本分析饼图
    plt.figure(figsize=(10, 8))

    # 提取数据
    names = [r['name'] for r in results_summary]
    costs = [r['cost'] for r in results_summary]

    # 计算百分比
    total = sum(costs)
    percentages = [cost/total*100 for cost in costs]

    # 绘制饼图
    plt.pie(percentages, labels=names, autopct='%1.1f%%', startangle=140, colors=colors)
    plt.title('四类垃圾运输成本占比分析', fontsize=16)

    # 保存图片
    plt.tight_layout()
    plt.savefig(f'{save_dir}/成本分析饼图.png', dpi=300)

# --- 运行最终方案！ ---
if __name__ == "__main__":
    solve_and_report()
